=== USUNIĘCIE MOŻLIWOŚCI ZWIJANIA GRUP W TABELI ZADAŃ ===

Data: 2025-07-30
Autor: Krystian
Opis: Usunię<PERSON> możliwość zwijania grup zadań w tabeli "Przegląd wszystkich zadań z całego systemu" w Dashboard

=== WPROWADZONE ZMIANY ===

1. **Usunięto stan collapsedGroups**
   - Usunięto useState dla zarządzania zwiniętymi grupami
   - Usunięto Set<string> przechowujący nazwy zwiniętych grup

2. **Usunięto funkcję toggleGroupCollapse**
   - Funkcja odpowiedzialna za przełączanie stanu zwijania grup
   - Nie jest już potrzebna po usunięciu funkcjonalności

3. **Zmodyfikowano logikę generowania danych tabeli**
   - Usunięto warunki sprawdzające czy grupa jest zwinięta
   - Wszystkie zadania są teraz zawsze wyświetlane
   - Komentarze zaktualizowane: "Always add all tasks (no collapsing)"

4. **Uproszczono nagłówki grup**
   - Usunięto przyciski z funkcją onClick
   - Usunięto ikony ChevronRight i ChevronDown
   - Nagłówki grup to teraz zwykłe div-y bez interakcji
   - Zachowano nazwę grupy i licznik zadań w Badge

5. **Usunięto nieużywane importy**
   - Usunięto import ChevronRight z lucide-react
   - Zachowano ChevronDown dla innych komponentów

6. **Poprawiono zależności useMemo**
   - Dodano brakujące zależności onTaskDetails i onTaskEdit
   - Usunięto collapsedGroups z dependency array

=== PLIKI ZMODYFIKOWANE ===

- src/components/dashboard/tasks-table.tsx
- README.md (dodano wpis o zmianie)
- docs/tasks-table-no-collapse.txt (ten plik)

=== UZASADNIENIE ZMIANY ===

1. **Lepsza przejrzystość** - Wszystkie zadania są zawsze widoczne
2. **Uproszczenie interfejsu** - Mniej elementów interaktywnych
3. **Szybszy dostęp do danych** - Brak potrzeby rozwijania grup
4. **Spójność z filozofią dashboard** - Przegląd wszystkich zadań powinien być kompletny

=== ZACHOWANE FUNKCJONALNOŚCI ===

- Grupowanie zadań według statusów
- Liczniki zadań w każdej grupie
- Możliwość ukrywania pustych grup
- Wszystkie inne funkcje tabeli (sortowanie, filtrowanie, edycja inline)
- Preferencje kolumn i ich kolejność

=== WPŁYW NA UŻYTKOWNIKÓW ===

- Pozytywny: Lepszy dostęp do wszystkich zadań
- Pozytywny: Prostszy interfejs
- Neutralny: Brak możliwości zwijania grup (funkcja rzadko używana)

=== TESTY ===

- Aplikacja kompiluje się bez błędów
- Dashboard ładuje się poprawnie
- Tabela zadań wyświetla wszystkie grupy w stanie rozwiniętym
- Wszystkie inne funkcje tabeli działają prawidłowo
