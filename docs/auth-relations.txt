RELACJE MIĘDZY KOMPONENTAMI UWIERZYTELNIANIA - Nexus

=== STRUKTURA UWIERZYTELNIANIA ===

1. STRONA REJESTRACJI (/auth/signup)
   - Plik: src/app/auth/signup/page.tsx
   - Funkcjonalność: Formularz rejestracji nowego użytkownika
   - API: POST /api/auth/register
   - Przekierowanie: Po pomyślnej rejestracji → /auth/signin?message=Konto zostało utworzone pomyślnie

2. STRONA LOGOWANIA (/auth/signin)
   - Plik: src/app/auth/signin/page.tsx
   - Funkcjonalność: Formularz logowania + wyświetlanie komunikatu o rejestracji
   - API: NextAuth.js credentials provider
   - Obsługa parametru URL: message (wyświetla toast z komunikatem)
   - Przekierowanie: Po pomyślnym logowaniu → /dashboard

3. API REJESTRACJI
   - Plik: src/app/api/auth/register/route.ts
   - Funkcjonalność: Tworzenie nowego użytkownika w bazie danych
   - Walidacja: Sprawdzanie czy użytkownik już istnieje
   - Hashowanie hasła: bcrypt

4. KONFIGURACJA NEXTAUTH
   - Plik: src/lib/auth.ts
   - Provider: Credentials (email + hasło)
   - Adapter: PrismaAdapter
   - Sesja: JWT strategy

=== PRZEPŁYW REJESTRACJI I LOGOWANIA ===

1. Użytkownik wypełnia formularz rejestracji
2. POST /api/auth/register → tworzy użytkownika w bazie
3. Przekierowanie na /auth/signin?message=Konto zostało utworzone pomyślnie
4. Strona logowania odczytuje parametr 'message' z URL
5. useEffect wyświetla toast.success() z komunikatem
6. URL zostaje oczyszczony z parametru (history.replaceState)
7. Użytkownik może się zalogować

=== SYSTEM POWIADOMIEŃ ===

- Biblioteka: sonner (toast notifications)
- Komponent: src/components/ui/sonner.tsx
- Dodany do layout: src/app/layout.tsx
- Użycie: toast.success(), toast.error()

=== PLIKI ZWIĄZANE Z UWIERZYTELNIANIEM ===

- src/app/auth/signup/page.tsx - Strona rejestracji
- src/app/auth/signin/page.tsx - Strona logowania
- src/app/api/auth/register/route.ts - API rejestracji
- src/app/api/auth/[...nextauth]/route.ts - NextAuth handler
- src/lib/auth.ts - Konfiguracja NextAuth
- src/components/ui/sonner.tsx - Komponent toastów
- src/app/layout.tsx - Layout z Toaster

=== ZALEŻNOŚCI ===

- next-auth: Uwierzytelnianie
- @auth/prisma-adapter: Adapter dla Prisma
- bcryptjs: Hashowanie haseł
- sonner: Toast notifications
- prisma: ORM do bazy danych

=== AKTUALIZACJA (2025-07-29) - SYSTEM RÓL I ZARZĄDZANIE UŻYTKOWNIKAMI ===

1. SYSTEM RÓL UŻYTKOWNIKÓW
   - Dodano pole 'role' do modelu User w schema.prisma
   - Dostępne role: 'user' (domyślna) i 'admin'
   - Konto <EMAIL> ustawione jako administrator
   - Rola przekazywana w sesji JWT i dostępna w całej aplikacji

2. MIDDLEWARE ADMINISTRATORA
   - src/lib/admin.ts - funkcje sprawdzające uprawnienia administratora
   - isAdmin() - sprawdza czy użytkownik ma uprawnienia admina
   - getAdminSession() - zwraca sesję tylko dla administratorów
   - isAdminEmail() - sprawdza konkretny email administratora

3. API ZARZĄDZANIA UŻYTKOWNIKAMI (tylko dla administratorów)
   - GET /api/admin/users - lista wszystkich użytkowników z paginacją i wyszukiwaniem
   - GET /api/admin/users/[userId] - szczegóły konkretnego użytkownika
   - PATCH /api/admin/users/[userId] - edycja użytkownika (dane, rola, hasło)
   - DELETE /api/admin/users/[userId] - usuwanie użytkownika

4. INTERFEJS ZARZĄDZANIA UŻYTKOWNIKAMI
   - src/components/settings/user-management.tsx - komponent zarządzania użytkownikami
   - Lista użytkowników z wyszukiwaniem i paginacją
   - Edycja danych użytkowników i zmiany ról
   - Usuwanie kont użytkowników (z potwierdzeniem)
   - Wyświetlanie statystyk użytkowników (zadania, zespoły, itp.)

5. INTEGRACJA Z USTAWIENIAMI
   - Dodano zakładkę "Użytkownicy" w ustawieniach (widoczna tylko dla administratorów)
   - Dynamiczne dostosowanie liczby kolumn w TabsList
   - Sprawdzanie uprawnień administratora w komponencie ustawień

=== AKTUALIZACJA (2025-07-28) ===

Dodano funkcjonalność wyświetlania komunikatu o pomyślnej rejestracji:
- Strona logowania obsługuje parametr URL 'message'
- Automatyczne wyświetlanie toast notification
- Czyszczenie URL po wyświetleniu komunikatu
- Używa systemu toastów Sonner już zaimplementowanego w aplikacji
- Zabezpieczenie przed wielokrotnym wyświetlaniem toasta (useRef)

=== ROZWIĄZANIE PROBLEMU PODWÓJNYCH TOASTÓW ===

Problem: W React 18 z Strict Mode komponenty renderują się dwukrotnie w trybie deweloperskim
Rozwiązanie: Użycie useRef do śledzenia czy toast już został wyświetlony
- toastShownRef.current = true po pierwszym wyświetleniu
- Sprawdzenie !toastShownRef.current przed wyświetleniem toasta

=== AKTUALIZACJA BEZPIECZEŃSTWA KONTA (2025-07-29) ===

Dodano funkcjonalności bezpieczeństwa konta w dashboard/settings:

1. ZMIANA HASŁA
   - Plik: src/app/api/user/change-password/route.ts
   - Funkcjonalność: Zmiana hasła użytkownika z walidacją
   - Walidacja: Obecne hasło, siła nowego hasła (min. 8 znaków)
   - Bezpieczeństwo: Hashowanie bcrypt, sprawdzenie czy nowe hasło różni się od obecnego

2. ZARZĄDZANIE AKTYWNYMI SESJAMI
   - Plik: src/app/api/user/sessions/route.ts
   - Funkcjonalność: Pobieranie i usuwanie aktywnych sesji użytkownika
   - Informacje o sesji: IP, urządzenie, przeglądarka, data utworzenia i wygaśnięcia
   - Możliwość zakończenia pojedynczej sesji lub wszystkich innych sesji

3. KOMPONENTY UI
   - src/components/settings/password-change-form.tsx - Formularz zmiany hasła
   - src/components/settings/active-sessions.tsx - Zarządzanie sesjami
   - Dodano zakładkę "Bezpieczeństwo" w src/components/settings/settings-content.tsx

4. MODELE BAZY DANYCH
   - Dodano modele NextAuth: Account, Session, VerificationToken
   - Rozszerzono model User o relacje accounts i sessions
   - Session zawiera dodatkowe pola: userAgent, ipAddress dla bezpieczeństwa

=== PLIKI DODANE/ZMODYFIKOWANE ===

NOWE PLIKI:
- src/app/api/user/change-password/route.ts - API zmiany hasła
- src/app/api/user/sessions/route.ts - API zarządzania sesjami
- src/components/settings/password-change-form.tsx - Komponent zmiany hasła
- src/components/settings/active-sessions.tsx - Komponent aktywnych sesji
- src/lib/admin.ts - Middleware sprawdzający uprawnienia administratora
- src/app/api/admin/users/route.ts - API listy użytkowników (admin)
- src/app/api/admin/users/[userId]/route.ts - API zarządzania konkretnym użytkownikiem (admin)
- src/components/settings/user-management.tsx - Komponent zarządzania użytkownikami
- scripts/create-admin.ts - Skrypt tworzący konto administratora

ZMODYFIKOWANE PLIKI:
- prisma/schema.prisma - Dodano modele NextAuth dla sesji + pole role w User
- src/components/settings/settings-content.tsx - Dodano zakładkę bezpieczeństwa + zarządzanie użytkownikami
- src/lib/auth.ts - Uwzględnienie roli użytkownika w sesji JWT
- src/types/next-auth.d.ts - Rozszerzenie typów NextAuth o pole role
