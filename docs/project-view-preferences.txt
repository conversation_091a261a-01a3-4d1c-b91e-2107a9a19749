=== PREFERENCJE WIDOKU PROJEKTÓW ===

Funkcjonalność zapisywania preferencji widoku (lista/tablica) dla każdego projektu w localStorage.

=== STRUKTURA DANYCH ===

Klucz localStorage: "Nexus-project-view-preferences"

Struktura danych:
{
  "project-id-1": "list",
  "project-id-2": "board",
  "project-id-3": "list"
}

Możliwe wartości widoku:
- "list" - widok listy zadań
- "board" - widok tablicy Kanban

=== PLIKI ZWIĄZANE Z FUNKCJONALNOŚCIĄ ===

1. src/hooks/use-project-view-preferences.ts
   - Hook do zarządzania preferencjami widoku projektów
   - Automatyczne wczytywanie z localStorage przy inicjalizacji
   - Funkcja updateViewMode do zmiany i zapisywania preferencji
   - Obsługa błędów przy problemach z localStorage

2. src/components/projects/project-details-content.tsx
   - Główny komponent szczegółów projektu
   - Używa hooka useProjectViewPreferences
   - Przełączniki widoku (List/LayoutGrid) zapisują preferencje
   - Loading uwzględnia ładowanie preferencji

=== PRZEPŁYW DZIAŁANIA ===

1. Użytkownik wchodzi na stronę projektu /dashboard/projects/[id]
2. Hook useProjectViewPreferences:
   - Wczytuje preferencje z localStorage dla danego projectId
   - Ustawia domyślny widok "list" jeśli brak preferencji
   - Oznacza jako załadowane (isLoaded: true)
3. Komponent czeka na załadowanie preferencji przed renderowaniem
4. Użytkownik klika przełącznik widoku (List/Board)
5. Hook zapisuje nową preferencję w localStorage
6. Widok się zmienia natychmiast
7. Po odświeżeniu strony widok zostaje przywrócony z localStorage

=== OBSŁUGA BŁĘDÓW ===

- Try/catch przy wczytywaniu z localStorage
- Try/catch przy zapisywaniu do localStorage
- Fallback do domyślnego widoku "list" przy błędach
- Console.warn dla błędów (nie blokuje działania aplikacji)

=== KORZYŚCI ===

- Lepsze UX - użytkownik nie musi za każdym razem przełączać widoku
- Personalizacja - każdy projekt może mieć inny preferowany widok
- Wydajność - localStorage jest szybki i nie wymaga zapytań do serwera
- Offline - działa bez połączenia z internetem

=== KOMPATYBILNOŚĆ ===

- Działa we wszystkich nowoczesnych przeglądarkach
- Graceful degradation - przy braku localStorage używa domyślnego widoku
- Nie wpływa na działanie aplikacji przy wyłączonym localStorage

=== PRZYSZŁE ROZSZERZENIA ===

Możliwe rozszerzenia funkcjonalności:
- Synchronizacja preferencji z serwerem (dla użytkowników zalogowanych)
- Preferencje filtrów zadań
- Preferencje sortowania
- Preferencje układu kolumn w widoku listy
- Export/import preferencji

=== TESTOWANIE ===

Aby przetestować funkcjonalność:
1. Przejdź do projektu w widoku listy
2. Przełącz na widok tablicy
3. Odśwież stronę - powinien zostać widok tablicy
4. Przełącz z powrotem na listę
5. Przejdź do innego projektu i z powrotem - preferencja powinna zostać zachowana

Sprawdzenie localStorage:
1. Otwórz DevTools (F12)
2. Zakładka Application/Storage
3. Local Storage -> localhost:3000
4. Klucz: Nexus-project-view-preferences
