# Relacje między elementami w Nexus

## Zabezpieczenia zarządzania użytkownikami (2025-08-01):

### Problem:
W systemie zarządzania użytkownikami administrator mógł edytować swoją własną rolę, co stanowiło zagrożenie bezpieczeństwa.

### Rozwiązanie:
1. **Zabezpieczenie API**:
   - Dodano sprawdzenie w `/api/admin/users/[userId]` PATCH endpoint
   - Blokada zmiany roli gdy `session.user.id === userId`
   - Zwracany błąd 400 z komunikatem "Cannot change your own role"

2. **Zabezpieczenie interfejsu**:
   - Dodano import `useSession` w komponencie `user-management.tsx`
   - Pole Select roli jest wyłączone (`disabled`) gdy edytowany użytkownik to aktualny użytkownik
   - Przycisk usuwania jest wyłączony dla własnego konta
   - Dodano komunikat informacyjny "Nie możesz zmienić swojej własnej roli"

### Pliki zmodyfikowane:
- `src/app/api/admin/users/[userId]/route.ts` - zabezpieczenie API
- `src/components/settings/user-management.tsx` - zabezpieczenie interfejsu
- `docs/user-roles-management.md` - aktualizacja dokumentacji

## Naprawa liczenia zadań w TasksTable (2025-07-30):

### Problem:
W komponencie TasksTable występowały problemy z liczeniem zadań - footer pokazywał nieprawidłową liczbę zadań (np. "0 z 5 zadań wybranych" gdy było 4 zadania w bazie).

### Przyczyny:
1. Funkcja `table.getFilteredRowModel().rows.length` liczyła wszystkie wiersze włącznie z nagłówkami grup statusów
2. Nagłówki grup mogły być zaznaczane jako wiersze
3. Mylący tekst "zadań wybranych"

### Rozwiązanie:
1. **Funkcje pomocnicze do liczenia rzeczywistych zadań**:
   - `getActualTasksCount()` - liczy tylko zadania (bez nagłówków grup)
   - `getSelectedTasksCount()` - liczy tylko zaznaczone zadania (bez nagłówków grup)

2. **Zapobieganie selekcji nagłówków grup**:
   - Dodano `enableRowSelection: (row) => !('isGroupHeader' in row.original)`

3. **Poprawa footer tabeli**:
   - Użycie nowych funkcji pomocniczych zamiast domyślnych z react-table
   - Zmiana tekstu na "zadań zaznaczonych" dla lepszej jasności

### Pliki zmodyfikowane:
- `src/components/dashboard/tasks-table.tsx` - główne poprawki liczenia i selekcji

## Naprawa przeglądu zadań w dashboard (2025-07-30):

### Problem:
Dashboard miał tytuł "Przegląd wszystkich zadań z całego systemu", ale API endpoint `/api/tasks` pobierał tylko zadania, do których użytkownik miał dostęp. To było mylące dla użytkowników, szczególnie administratorów.

### Rozwiązanie:
1. **Modyfikacja API endpoint `/api/tasks`**:
   - Dodano sprawdzanie uprawnień administratora za pomocą funkcji `isAdmin()`
   - Administrator widzi wszystkie zadania z systemu (z projektów niearchiwalnych + zadania bez projektów)
   - Zwykły użytkownik widzi tylko zadania do których ma dostęp (jak wcześniej)

2. **Nowy endpoint `/api/user/admin-status`**:
   - Endpoint do sprawdzania statusu administratora po stronie klienta
   - Zwraca informacje o uprawnieniach użytkownika

3. **Dynamiczny tytuł dashboard**:
   - Administrator: "Przegląd wszystkich zadań z całego systemu"
   - Zwykły użytkownik: "Przegląd moich zadań"
   - Tytuł zmienia się automatycznie w zależności od uprawnień

### Pliki zmodyfikowane:
- `src/app/api/tasks/route.ts` - logika pobierania zadań z uwzględnieniem uprawnień
- `src/components/dashboard/content.tsx` - dynamiczny tytuł i sprawdzanie statusu administratora
- `src/app/api/user/admin-status/route.ts` - nowy endpoint (utworzony)

### Konta testowe:
- Administrator: `<EMAIL>` / `admin123`
- Zwykły użytkownik: `<EMAIL>` / `password123`

## Poprawka breadcrumbs w szczegółach projektu (2025-01-30):

### Problem:
W breadcrumbs w szczegółach projektu wyświetlało się ID projektu zamiast jego nazwy.

### Rozwiązanie:
Zmodyfikowano komponent `DashboardBreadcrumbs` (`src/components/dashboard/breadcrumbs.tsx`):

1. **Wykorzystanie kontekstu projektów**:
   - Dodano import `useProjects` z kontekstu projektów
   - Usunięto niezależne ładowanie projektów przez fetch API
   - Breadcrumbs teraz korzysta z globalnego kontekstu projektów

2. **Dodano fallback dla brakujących projektów**:
   - Dodano stan `missingProjects` do cache'owania nazw projektów
   - Dodano funkcję `fetchMissingProject` do pobierania nazw projektów które nie są w kontekście
   - Automatyczne pobieranie nazwy projektu jeśli nie jest dostępna w kontekście

3. **Ulepszona obsługa stanów ładowania**:
   - Wyświetlanie "Ładowanie..." gdy dane są jeszcze pobierane
   - Wyświetlanie "Projekt" zamiast ID gdy projekt nie jest znaleziony
   - Lepsze rozpoznawanie segmentów URL związanych z projektami

### Przepływ działania:
1. Breadcrumbs sprawdza segmenty URL
2. Dla segmentów projektów (UUID) próbuje znaleźć nazwę w kontekście projektów
3. Jeśli nie znajdzie, sprawdza cache `missingProjects`
4. Jeśli nadal nie ma nazwy, pobiera ją z API i zapisuje w cache
5. Wyświetla odpowiednią nazwę lub placeholder w zależności od stanu

### Korzyści:
- **Szybsze ładowanie**: Wykorzystanie globalnego kontekstu projektów
- **Lepsze UX**: Wyświetlanie nazw projektów zamiast ID
- **Fallback**: Automatyczne pobieranie brakujących nazw projektów
- **Cache**: Unikanie wielokrotnych zapytań API dla tego samego projektu
- **Responsywność**: Lepsze oznaczenia podczas ładowania danych

## Naprawka błędu TypeError w ProjectInfoContent (2025-07-30):

### Problem:
TypeError: Cannot read properties of undefined (reading 'length') w komponencie ProjectInfoContent.

### Przyczyna:
Funkcja `getTaskStats` próbowała odczytać właściwość 'length' z undefined/null wartości gdy `project.tasks` było niezdefiniowane.

### Rozwiązanie:
Zmodyfikowano komponent `ProjectInfoContent` (`src/components/projects/project-info-content.tsx`):

1. **Dodano sprawdzenie null/undefined w getTaskStats**:
   - Funkcja teraz sprawdza czy tasks jest tablicą przed dostępem do właściwości
   - Zwraca domyślne wartości (0) gdy tasks jest undefined/null

2. **Zaktualizowano interfejs ProjectDetails**:
   - Zmieniono `tasks` i `members` na opcjonalne właściwości (tasks?, members?)
   - Lepsze odzwierciedlenie rzeczywistych danych z API

3. **Dodano bezpieczne sprawdzenia dla team.members**:
   - Użycie optional chaining (?.) w dostępie do members.length
   - Dodano fallback UI gdy brak członków zespołu

### Korzyści:
- **Stabilność**: Eliminacja błędów runtime związanych z undefined properties
- **Defensive Programming**: Bezpieczne sprawdzenia przed dostępem do danych
- **Lepsze UX**: Graceful degradation zamiast crash aplikacji
- **Kompatybilność**: Działa z projektami z i bez zadań/członków

## Implementacja prawego sidebara z ostatnimi zmianami (2025-07-30):

### Funkcjonalność:
Dodano prawy sidebar o szerokości 400px z możliwością zwinięcia, który wyświetla ostatnie zmiany systemowe. Administratorzy mogą dodawać nowe zmiany bezpośrednio z interfejsu.

### Komponenty dodane:
1. **Model bazy danych SystemChange** (`prisma/schema.prisma`):
   - Tabela do przechowywania zmian systemowych
   - Pola: id, title, description, type, isVisible, createdAt, updatedAt, createdById
   - Relacja z modelem User (createdBy)

2. **API endpoints**:
   - `GET /api/system-changes` - pobieranie widocznych zmian (dla wszystkich użytkowników)
   - `GET /api/admin/system-changes` - zarządzanie zmianami (tylko admin)
   - `POST /api/admin/system-changes` - dodawanie nowych zmian (tylko admin)
   - `PATCH /api/admin/system-changes/[id]` - edycja zmian (tylko admin)
   - `DELETE /api/admin/system-changes/[id]` - usuwanie zmian (tylko admin)

3. **Komponenty UI**:
   - `RightSidebar` - prawy sidebar z możliwością zwinięcia
   - `RecentChanges` - lista ostatnich zmian z różnymi typami (info, warning, success, error)
   - `SystemChangeForm` - formularz dodawania nowych zmian (tylko dla adminów)
   - `NavAdmin` - sekcja administracyjna w lewym sidebarze (tylko dla adminów)

4. **Integracja z layoutem**:
   - Modyfikacja `DashboardLayout` do obsługi prawego sidebara
   - Automatyczne dostosowanie szerokości głównej treści gdy sidebar jest widoczny
   - Przycisk "Ostatnie zmiany" w sekcji administracyjnej lewego sidebara

### Uproszczenie systemu zmian:
- Usunięto pole "typ zmiany" - wszystkie zmiany mają domyślny typ "info"
- Usunięto pole "widoczność" - wszystkie zmiany są zawsze widoczne dla użytkowników
- Uproszczony formularz zawiera tylko tytuł i opis

### Uprawnienia:
- **Wszyscy użytkownicy**: mogą przeglądać wszystkie zmiany systemowe
- **Administratorzy**: mogą dodawać, edytować i usuwać zmiany systemowe

### Pliki dodane/zmodyfikowane:
- `prisma/schema.prisma` - model SystemChange
- `src/types/index.ts` - interfejs SystemChange (dodano isVisible)
- `src/app/api/system-changes/route.ts` - publiczny endpoint
- `src/app/api/admin/system-changes/route.ts` - endpoint administracyjny
- `src/app/api/admin/system-changes/[changeId]/route.ts` - CRUD dla pojedynczych zmian (DELETE endpoint)
- `src/components/dashboard/right-sidebar.tsx` - prawy sidebar
- `src/components/dashboard/recent-changes.tsx` - lista zmian (dodano usuwanie)
- `src/components/dashboard/nav-admin.tsx` - nawigacja administracyjna
- `src/components/admin/system-change-form.tsx` - formularz dodawania zmian
- `src/components/dashboard/layout.tsx` - integracja z layoutem (poprawiono szerokość)
- `src/app/globals.css` - dodano style dla szerokości głównej treści
- `scripts/seed-system-changes.ts` - przykładowe dane

### Ostatnie zmiany (2025-01-08):
1. **Funkcjonalność usuwania dla administratorów**:
   - Dodano przycisk usuwania (ikona kosza) obok przycisku ukrywania/pokazywania
   - Dodano funkcję `deleteChange` z potwierdzeniem usunięcia
   - Wykorzystano istniejący endpoint DELETE `/api/admin/system-changes/[changeId]`
   - Dodano właściwość `isVisible` do interfejsu SystemChange

2. **Poprawiono układ szerokości głównej treści**:
   - Dodano `overflow-x-auto` do głównego kontenera
   - Dodano wrapper `min-w-0 w-full` dla dzieci głównej treści
   - Dodano style CSS zapobiegające poziomemu scrollowaniu całej strony
   - Tabele i szerokie komponenty mają teraz własny poziomy scroll

## SYSTEM PRZYPOMNIEŃ O ZADANIACH (2025-08-01)

### Opis funkcjonalności:
System umożliwia użytkownikom ustawienie powiadomień push przeglądarki, które będą wysyłane przed terminem wykonania zadania. Użytkownik może wybrać czy chce otrzymać przypomnienie na określoną liczbę godzin lub dni przed zadaniem.

### Komponenty systemu:

#### 1. Rozszerzenie modelu Task
- `reminderEnabled` (Boolean) - czy przypomnienie jest włączone
- `reminderTime` (DateTime) - dokładny czas wysłania przypomnienia
- `reminderType` (String) - typ jednostki ("hours" lub "days")
- `reminderValue` (Int) - liczba godzin/dni przed terminem

#### 2. Model PushSubscription
- Przechowuje subskrypcje push użytkowników
- Relacja z User (jeden użytkownik może mieć wiele subskrypcji)
- Pola: endpoint, p256dh, auth, userAgent

#### 3. Service Worker (`public/sw.js`)
- Obsługuje powiadomienia push przeglądarki
- Reaguje na kliknięcia w powiadomienia
- Przekierowuje do odpowiedniego zadania

#### 4. Hook `usePushNotifications`
- Zarządza subskrypcjami push
- Sprawdza wsparcie przeglądarki
- Obsługuje uprawnienia

#### 5. Komponent `ReminderSettings`
- Interfejs do konfiguracji przypomnień w formularzu zadania
- Walidacja uprawnień i terminu zadania
- Podgląd czasu przypomnienia

### Pliki dodane/zmodyfikowane:
- `prisma/schema.prisma` - rozszerzenie modelu Task i dodanie PushSubscription
- `src/types/index.ts` - rozszerzenie interfejsu Task
- `src/components/tasks/reminder-settings.tsx` - komponent ustawień
- `src/components/shared/task-form-content.tsx` - integracja z formularzem
- `src/hooks/usePushNotifications.ts` - hook do zarządzania push notifications
- `public/sw.js` - service worker
- `src/components/providers/service-worker-provider.tsx` - rejestracja SW
- `src/app/layout.tsx` - dodanie ServiceWorkerProvider
- `src/app/api/push/vapid-key/route.ts` - endpoint dla klucza VAPID
- `src/app/api/push/subscribe/route.ts` - endpoint subskrypcji
- `src/app/api/push/unsubscribe/route.ts` - endpoint anulowania subskrypcji
- `src/app/api/push/send/route.ts` - endpoint wysyłania powiadomień
- `src/app/api/reminders/check/route.ts` - endpoint sprawdzania przypomnień
- `src/app/api/tasks/route.ts` - rozszerzenie o pola przypomnienia
- `src/app/api/tasks/[taskId]/route.ts` - rozszerzenie o pola przypomnienia
- `docs/task-reminders.md` - dokumentacja funkcjonalności

## Kompleksowe Seeds - Dane Testowe (2025-08-01):

### Przygotowano rozszerzone seeds w `prisma/seed.ts` zawierające:

**Użytkownicy (6 osób z różnymi rolami):**
- Administrator: <EMAIL> (hasło: admin123)
- Team Lead: <EMAIL> (hasło: password123)
- Frontend Developer: <EMAIL> (hasło: password123)
- UI/UX Designer: <EMAIL> (hasło: password123)
- Backend Developer: <EMAIL> (hasło: password123)
- DevOps Engineer: <EMAIL> (hasło: password123)

**Zespoły (3 zespoły specjalistyczne):**
- Main Development Team (admin, john, jane, alice)
- Design & UX Team (jane, bob)
- DevOps & Infrastructure (alice, charlie)

**Statusy zadań (6 statusów z kolorami):**
- To Do (domyślny, szary #6B7280)
- In Progress (niebieski #3B82F6)
- In Review (pomarańczowy #F59E0B)
- Testing (fioletowy #8B5CF6)
- Done (zielony #10B981)
- Blocked (czerwony #EF4444)

**Projekty (4 różnorodne projekty):**
- Nexus - Project Management Platform (główny projekt z pełną konfiguracją)
- Nexus Mobile App (aplikacja mobilna)
- Analytics Dashboard (dashboard analityczny)
- Cloud Infrastructure (infrastruktura DevOps)

**Zadania (15 zadań w różnych stanach):**
- Zadania ukończone z czasem rozpoczęcia i zakończenia
- Zadania w trakcie realizacji
- Zadania w review i testowaniu
- Zadania do wykonania
- Przykład zablokowanego zadania z powodem blokady
- Różne priorytety (High, Medium, Low)
- Precyzyjne terminy wykonania z godziną (datetime)
- Różne godziny zakończenia: 14:00, 15:00, 16:00, 17:00, 18:00 UTC

**Subtaski (28 podzadań):**
- Szczegółowy podział pracy dla głównych zadań
- Różne stany ukończenia
- Realistyczne kroki implementacji

**Komentarze (8 komentarzy):**
- Komunikacja zespołowa między członkami
- Feedback i uwagi do zadań
- Statusy postępu prac
- Przykłady współpracy

**Wpisy czasu (25 szczegółowych wpisów):**
- Realistyczne godziny pracy (1-6h dziennie)
- Szczegółowe opisy wykonanych prac
- Wpisy dla różnych użytkowników i zadań
- Śledzenie postępu w czasie

**Dokumenty projektowe (6 dokumentów):**
- Wymagania projektowe (PDF, 2MB)
- Dokumentacja API (Markdown, 512KB)
- System designu (Figma, 15MB)
- Wireframes mobilne (PDF, 8MB)
- Diagram infrastruktury (PNG, 1MB)
- Przewodnik wdrażania (Markdown, 256KB)

**Zmiany systemowe (6 wpisów changelog):**
- Różne typy komunikatów (success, info, warning)
- Chronologiczne wpisy o zmianach w systemie
- Komunikaty dla użytkowników o nowych funkcjach

**Todos (8 list kontrolnych):**
- Listy zadań do wykonania w ramach głównych tasków
- Różne stany ukończenia
- Szczegółowe kroki implementacji

### Uruchomienie seeds:
```bash
npm run db:seed
```

### Scenariusze testowe zawarte w seeds:
- **Różne role użytkowników** z pełnymi profilami
- **Zespoły specjalistyczne** z różnymi składami
- **Projekty w różnych fazach** (planowanie, rozwój, utrzymanie)
- **Zadania we wszystkich możliwych statusach**
- **Realistyczne śledzenie czasu** z historią pracy
- **Współpraca zespołowa** przez komentarze i subtaski
- **Dokumentacja projektowa** różnych typów
- **Komunikacja systemowa** przez changelog
- **Przykłady problemów** (zablokowane zadania)
- **Kompletne workflow** od planowania do wdrożenia
