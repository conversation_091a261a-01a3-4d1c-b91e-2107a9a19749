# Relacje między elementami systemu TeamFlow

## Funkcjonalności czasowe w zadaniach

### Pola czasowe w zadaniach
- **startTime** (DateTime?) - <PERSON><PERSON> rozpoczęcia zadania
- **endTime** (DateTime?) - <PERSON><PERSON> zakończenia zadania
- **dueDate** (DateTime?) - Termin wykonania zadania
- **createdAt** (DateTime) - Data utworzenia zadania
- **updatedAt** (DateTime) - Data ostatniej aktualizacji zadania

### Komponenty UI do obsługi dat i czasu
- **DatePicker** - Komponent do wyboru daty (używa react-day-picker z lokalizacją polską)
- **TimePicker** - Komponent do wyboru godziny i minut (15-minutowe interwały: 00, 15, 30, 45)
- **DateTimePicker** - Komponent łączący DatePicker i TimePicker, zwraca obiekt Date
- **ProjectDailyView** - Widok dzienny zadań z podziałem na osoby i godziny

### Formatowanie dat
- **formatTaskDueDate()** - Formatuje termin wykonania (dzień i miesiąc dla bieżącego roku)
- **formatTaskDueDateWithRelative()** - Dodaje względne opisy (Dzisiaj, Jutro)
- **formatCreatedDate()** - Formatuje datę utworzenia w krótkim formacie
- **dateToLocalDateString()** - Konwertuje Date na string YYYY-MM-DD bez problemów ze strefą czasową

### Widoki projektów
- **list** - Lista zadań
- **board** - Tablica Kanban
- **gantt** - Wykres Gantta
- **daily** - Nowy widok dzienny z harmonogramem godzinowym

### Relacje między komponentami

#### ProjectDetailsContent
- Zawiera przełącznik widoków (list/board/gantt/daily)
- Renderuje ProjectDailyView dla widoku "daily"
- Przekazuje zadania, członków zespołu i funkcje callback

#### ProjectDailyView
- Wyświetla zadania w układzie godzinowym (8:00-18:00)
- Grupuje zadania według przypisanych osób
- Pokazuje czas rozpoczęcia i zakończenia zadań
- Umożliwia nawigację między dniami
- Integruje się z DatePicker do wyboru dnia

#### TaskFormContent
- Rozszerzony o pola startTime i endTime
- Używa DateTimePicker do wprowadzania czasów
- Waliduje i wysyła dane czasowe do API

#### API Endpoints
- **POST /api/tasks** - obsługuje startTime i endTime przy tworzeniu zadań
  - Konwertuje ISO string na Date: `new Date(startTime)`, `new Date(endTime)`
  - Zapisuje w bazie danych jako DateTime
- **PATCH /api/tasks/[taskId]** - obsługuje startTime i endTime przy edycji zadań
  - Waliduje uprawnienia użytkownika
  - Aktualizuje tylko przekazane pola

### Przepływ danych datetime

1. **Tworzenie zadania z czasem:**
   ```
   DateTimePicker (Date object) →
   TaskFormContent (toISOString()) →
   API POST (new Date()) →
   Prisma (DateTime) →
   Database
   ```

2. **Edycja zadania z czasem:**
   ```
   Database (DateTime) →
   API GET (ISO string) →
   TaskFormContent (new Date()) →
   DateTimePicker (Date object) →
   TaskFormContent (toISOString()) →
   API PATCH (new Date()) →
   Prisma (DateTime) →
   Database
   ```

3. **Wyświetlanie dat:**
   ```
   Database (DateTime) →
   API GET (ISO string) →
   formatTaskDueDateWithRelative() →
   UI (sformatowany tekst)
   ```

### Walidacja i obsługa błędów datetime

1. **Walidacja w komponencie DateTimePicker:**
   - Sprawdza czy wybrana data jest prawidłowa
   - Obsługuje przypadki gdy data lub czas nie są wybrane
   - Zwraca `undefined` dla nieprawidłowych wartości

2. **Walidacja w API:**
   - Sprawdza czy przekazane stringi można skonwertować na Date
   - Obsługuje przypadki `null` i `undefined`
   - Zwraca błędy 400 dla nieprawidłowych danych

3. **Obsługa stref czasowych:**
   - Wszystkie daty są przechowywane w UTC w bazie danych
   - Konwersja na czas lokalny odbywa się w komponencie UI
   - Używa `toISOString()` do wysyłania dat do API

### Najlepsze praktyki

1. **Zawsze używaj `toISOString()` przy wysyłaniu dat do API**
2. **Sprawdzaj czy data istnieje przed konwersją: `date ? date.toISOString() : undefined`**
3. **W API używaj `new Date()` do konwersji ISO string na Date object**
4. **Używaj polskiej lokalizacji w komponentach dat: `locale: pl`**
5. **Dla dat bez czasu ustaw godziny na 00:00:00: `setHours(0, 0, 0, 0)`**
6. **Dla dat bez czasu używaj `dateToLocalDateString()` zamiast `date.toString()` aby uniknąć problemów ze strefą czasową**

### Naprawione błędy

#### Problem z datą o jeden dzień mniejszą (2025-01-03)
- **Problem**: W `task-details-content.tsx` używano `date?.toString()` do konwersji daty, co powodowało problemy ze strefą czasową
- **Rozwiązanie**: Zamieniono na `dateToLocalDateString(date)` w linii 744
- **Lokalizacja**: `src/components/tasks/task-details-content.tsx:744`
- **Funkcja**: `dateToLocalDateString()` używa wartości lokalnych zamiast UTC, zapobiegając przesunięciu daty

3. **Wyświetlanie widoku dziennego:**
   ProjectDetailsContent → ProjectDailyView → filtrowanie zadań → renderowanie harmonogramu

4. **Nawigacja w widoku dziennym:**
   ProjectDailyView → DatePicker → aktualizacja selectedDate → ponowne filtrowanie

### Struktura bazy danych

```sql
Task {
  id: String (PK)
  title: String
  description: String?
  statusId: String?
  priority: String?
  dueDate: DateTime?
  startTime: DateTime?  -- NOWE POLE
  endTime: DateTime?    -- NOWE POLE
  estimatedHours: Float?
  createdAt: DateTime
  updatedAt: DateTime
  projectId: String?
  assigneeId: String?
  createdById: String?
  -- pozostałe pola...
}
```

### Logika biznesowa

#### Filtrowanie zadań w widoku dziennym:
- Zadania z startTime lub endTime w wybranym dniu
- Zadania z dueDate w wybranym dniu (jako fallback)
- Grupowanie według assigneeId

#### Wyświetlanie w slotach czasowych:
- Zadania z określonym czasem: wyświetlane jako ciągłe elementy z odpowiednią wysokością
- Zadania bez czasu: wyświetlane w pierwszym slocie (8:00)
- Zadania trwające kilka godzin: wyświetlane jako jeden element o wysokości proporcjonalnej do czasu trwania
- Pozycjonowanie absolutne: zadania są nakładane na siatkę czasową jako overlay

#### Walidacja czasowa:
- endTime powinien być późniejszy niż startTime (opcjonalna walidacja)
- Czasy są przechowywane jako DateTime z pełną informacją o dacie i czasie

### Algorytm renderowania ciągłych zadań

#### Funkcje pomocnicze:
- **getTaskHeight()** - oblicza wysokość zadania na podstawie czasu trwania (60px na godzinę)
- **getTaskTopOffset()** - oblicza przesunięcie od góry slotu na podstawie minut rozpoczęcia
- **tasksOverlap()** - sprawdza czy dwa zadania nakładają się czasowo
- **calculateTaskLayout()** - algorytm układania nakładających się zadań

#### Struktura renderowania:
1. **Siatka tła** - statyczna siatka godzinowa (8:00-18:00)
2. **Overlay zadań** - absolutnie pozycjonowane zadania nakładane na siatkę
3. **Ciągłe elementy** - zadania trwające kilka godzin jako jeden element
4. **Kolumny dla kolizji** - nakładające się zadania w osobnych kolumnach

#### Algorytm wykrywania kolizji:
1. **Sortowanie** - zadania sortowane według czasu rozpoczęcia
2. **Algorytm kolumn** - każde zadanie umieszczane w pierwszej dostępnej kolumnie
3. **Sprawdzanie nakładania** - porównanie z ostatnim zadaniem w kolumnie
4. **Tworzenie nowych kolumn** - gdy brak miejsca w istniejących

#### Pozycjonowanie z kolizjami:
- **top**: `(startHour - 8) * 61px + minuteOffset + 8px`
- **height**: `durationInHours * 60px - 4px` (minus padding)
- **left**: `(100 / totalColumns) * columnIndex`%
- **width**: `(100 / totalColumns - 2)`% (minus margines)
- **z-index**: `10 + columnIndex` (wyższy dla późniejszych kolumn)

#### Przykłady działania:

**Scenariusz 1: Dwa nakładające się zadania**
- Zadanie A: 9:00-11:00
- Zadanie B: 10:00-12:00
- Rezultat: Każde zadanie ma 50% szerokości, A w lewej kolumnie, B w prawej

**Scenariusz 2: Trzy zadania z częściowym nakładaniem**
- Zadanie A: 9:00-10:00
- Zadanie B: 9:30-11:30
- Zadanie C: 11:00-12:00
- Rezultat: A i B w osobnych kolumnach (50% każde), C w pierwszej kolumnie (pełna szerokość)

**Scenariusz 3: Zadania bez czasu**
- Zadania bez startTime wyświetlane w pierwszej kolumnie o 8:00
- Zachowują proporcjonalną szerokość jeśli są zadania z czasem

### Funkcjonalność Drag and Drop

#### Komponenty drag and drop:
- **DraggableTask** - komponent zadania z możliwością przeciągania
- **DroppableTimeSlot** - slot czasowy jako cel upuszczenia
- **DragOverlay** - podgląd przeciąganego zadania

#### Biblioteka @dnd-kit:
- **DndContext** - kontekst drag and drop dla całego widoku
- **useDraggable** - hook dla przeciąganych zadań
- **useDroppable** - hook dla slotów czasowych
- **PointerSensor** - sensor do obsługi myszy/dotyku

#### Logika drag and drop:
1. **Rozpoczęcie przeciągania** - zapisanie aktywnego zadania
2. **Upuszczenie** - parsowanie ID celu (assigneeId-hour)
3. **Obliczanie nowego czasu** - na podstawie slotu docelowego
4. **Zachowanie czasu trwania** - jeśli zadanie ma endTime
5. **Aktualizacja przypisania** - zmiana assignee przy upuszczeniu na inną osobę

#### API aktualizacji:
- **PATCH /api/tasks/[taskId]** - aktualizacja startTime, endTime, assigneeId
- **Optymistyczne aktualizacje** - natychmiastowe odświeżenie UI
- **Obsługa błędów** - rollback w przypadku niepowodzenia

#### Przykłady działania drag and drop:

**Scenariusz 1: Przesunięcie zadania w czasie**
- Zadanie 9:00-11:00 → przeciągnięcie na 14:00
- Rezultat: zadanie 14:00-16:00 (zachowany czas trwania)

**Scenariusz 2: Zmiana przypisania**
- Zadanie Użytkownika A → przeciągnięcie na kolumnę Użytkownika B
- Rezultat: zmiana assigneeId + zachowanie czasu

**Scenariusz 3: Zadanie bez endTime**
- Zadanie tylko z startTime → przeciągnięcie
- Rezultat: nowy startTime, endTime ustawiony na +1 godzina

### Optimistic Updates i Powiadomienia

#### Optimistic UI:
- **Natychmiastowe aktualizacje** - UI aktualizuje się przed potwierdzeniem z serwera
- **State optimisticTasks** - lokalna kopia zadań z natychmiastowymi zmianami
- **Rollback na błąd** - przywrócenie oryginalnego stanu przy niepowodzeniu
- **Wizualny feedback** - animacja pulse dla aktualizowanych zadań

#### Powiadomienia Sonner:
- **Toast loading** - "Przenoszenie zadania na 14:00 (Jan)..." podczas operacji
- **Toast success** - "Zadanie przeniesione na 14:00" po sukcesie
- **Toast error** - "Nie udało się przenieść zadania" przy błędzie
- **Unikalne ID** - każdy toast ma ID `move-task-${taskId}` dla zastępowania
- **NAPRAWIONO** - Problem z nie znikającymi spinnerami w TasksKanbanBoard (2025-08-01)

#### Przepływ optimistic updates:
1. **Rozpoczęcie drag** - zapisanie activeTask
2. **Drop** - natychmiastowa aktualizacja optimisticTasks
3. **Loading toast** - wyświetlenie powiadomienia o trwającej operacji
4. **API call** - wysłanie żądania do serwera
5. **Success/Error** - aktualizacja toast + ewentualny rollback
6. **Cleanup** - usunięcie z updatingTasks

#### Wizualny feedback:
- **Opacity 0.7** - zadania w trakcie aktualizacji
- **Animate pulse** - pulsująca animacja dla updatingTasks
- **Disabled click** - brak możliwości kliknięcia podczas aktualizacji
- **Cursor grab** - wskazanie możliwości przeciągania

#### Synchronizacja stanu:
- **useEffect** - synchronizacja optimisticTasks z props.tasks
- **updatingTasks Set** - śledzenie zadań w trakcie aktualizacji
- **Rollback mechanism** - przywrócenie originalTask przy błędzie
