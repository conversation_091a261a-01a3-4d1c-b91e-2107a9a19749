import { redirect } from "next/navigation"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { DashboardLayout } from "@/components/dashboard/layout"
import { ProjectDetailsContent } from "@/components/projects/project-details-content"

interface ProjectPageProps {
  params: Promise<{
    projectId: string
  }>
}

export default async function ProjectPage({ params }: ProjectPageProps) {
  const session = await getServerSession(authOptions)

  if (!session) {
    redirect("/auth/signin")
  }

  const { projectId } = await params

  return (
    <DashboardLayout>
      <ProjectDetailsContent projectId={projectId} />
    </DashboardLayout>
  )
}
